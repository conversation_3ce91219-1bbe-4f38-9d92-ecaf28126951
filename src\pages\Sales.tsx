
import { ShoppingCart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Link } from 'react-router-dom';
import CurrencyFormatter from '@/components/ui/currency-formatter';
import Header from '@/components/Header';
import { saleProducts } from '@/data/products';
import { useCart } from '@/contexts/CartContext';

const Sales = () => {
  const { addItem } = useCart();

  const handleAddToCart = (product: typeof saleProducts[0]) => {
    addItem({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      category: product.category
    });
  };

  const calculateSavings = (original: number, sale: number) => {
    return (original - sale).toFixed(0);
  };

  return (
    <div className="min-h-screen bg-forest-grey-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-red-600 to-red-700 rounded-lg p-8 mb-8 text-white">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">🔥 Hot Sales 🔥</h1>
            <p className="text-xl mb-6 text-red-100">
              Limited time offers on premium building materials. Save big on your next project!
            </p>
            <div className="flex justify-center space-x-4 text-sm">
              <div className="bg-white bg-opacity-20 rounded-lg px-4 py-2">
                <span className="font-bold">Up to 25% OFF</span>
              </div>
              <div className="bg-white bg-opacity-20 rounded-lg px-4 py-2">
                <span className="font-bold">Free Delivery</span>
              </div>
              <div className="bg-white bg-opacity-20 rounded-lg px-4 py-2">
                <span className="font-bold">Limited Stock</span>
              </div>
            </div>
          </div>
        </div>

        {/* Sale Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-forest-green-600 mb-2">25%</div>
            <div className="text-forest-grey-600">Maximum Discount</div>
          </div>
          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-forest-green-600 mb-2">{saleProducts.length}</div>
            <div className="text-forest-grey-600">Items on Sale</div>
          </div>
          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-forest-green-600 mb-2">
              <CurrencyFormatter amount={saleProducts.reduce((total, product) => total + parseFloat(calculateSavings(product.originalPrice, product.price)), 0)} />
            </div>
            <div className="text-forest-grey-600">Total Possible Savings</div>
          </div>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {saleProducts.map((product) => (
            <Card key={product.id} className="group hover:shadow-lg transition-all duration-300 relative overflow-hidden">
              <div className="absolute top-2 left-2 z-10">
                <Badge className="bg-red-500 text-white font-bold">
                  {product.discount}% OFF
                </Badge>
              </div>
              
              {!product.inStock && (
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-20">
                  <span className="text-white font-bold text-lg">SOLD OUT</span>
                </div>
              )}

              <div className="relative overflow-hidden rounded-t-lg">
                <img 
                  src={product.image} 
                  alt={product.name}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                {product.quantityLeft <= 5 && product.inStock && (
                  <div className="absolute bottom-2 right-2 bg-orange-500 text-white px-2 py-1 rounded text-xs font-medium">
                    Only {product.quantityLeft} left!
                  </div>
                )}
              </div>

              <CardContent className="p-4">
                <div className="text-sm text-forest-green-600 font-medium mb-1">{product.category}</div>
                <h3 className="text-lg font-semibold text-forest-grey-800 mb-3 line-clamp-2">{product.name}</h3>
                
                <div className="space-y-2 mb-4">
                  <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold text-forest-green-600">
                      <CurrencyFormatter amount={product.price} />
                    </span>
                    <span className="text-lg text-forest-grey-500 line-through">
                      <CurrencyFormatter amount={product.originalPrice} />
                    </span>
                  </div>
                  <div className="text-sm">
                    <span className="text-forest-green-600 font-medium">
                      You save: <CurrencyFormatter amount={parseInt(calculateSavings(product.originalPrice, product.price))} />
                    </span>
                  </div>
                </div>

                <Button
                  className="w-full"
                  disabled={!product.inStock}
                  variant={product.inStock ? "default" : "secondary"}
                  onClick={() => product.inStock && handleAddToCart(product)}
                >
                  <ShoppingCart className="w-4 h-4 mr-2" />
                  {product.inStock ? 'Add to Cart' : 'Sold Out'}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Call to Action */}
        <div className="mt-16 bg-forest-green-600 rounded-lg p-8 text-white text-center">
          <h2 className="text-3xl font-bold mb-4">Don't Miss Out!</h2>
          <p className="text-xl mb-6 text-forest-green-100">
            These deals won't last long. Stock up on quality materials while prices are low.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="outline" className="text-white border-white hover:bg-white hover:text-forest-green-600" asChild>
              <Link to="/products">View All Products</Link>
            </Button>
            <Button size="lg" className="bg-white text-forest-green-600 hover:bg-forest-grey-100" asChild>
              <Link to="/contact">Contact for Bulk Pricing</Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sales;
