import React, { useState } from 'react';
import { Phone, Mail, MapPin, Clock, Send } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import Header from '@/components/Header';

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // For now, just show an alert - this will be connected to backend later
    alert('Thank you for your message! We will get back to you soon.');
    setFormData({
      name: '',
      email: '',
      phone: '',
      subject: '',
      message: ''
    });
  };

  return (
    <div className="min-h-screen bg-forest-grey-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-forest-grey-800 mb-4">Contact Us</h1>
          <p className="text-lg text-forest-grey-600">
            Get in touch with our team for bulk pricing, delivery inquiries, or any questions about our products.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Contact Information */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="text-forest-grey-800">Get in Touch</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-start space-x-3">
                  <Phone className="w-5 h-5 text-forest-green-600 mt-1" />
                  <div>
                    <h3 className="font-semibold text-forest-grey-800">Phone</h3>
                    <p className="text-forest-grey-600">+254 700 123 456</p>
                    <p className="text-forest-grey-600">+254 733 987 654</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <Mail className="w-5 h-5 text-forest-green-600 mt-1" />
                  <div>
                    <h3 className="font-semibold text-forest-grey-800">Email</h3>
                    <p className="text-forest-grey-600"><EMAIL></p>
                    <p className="text-forest-grey-600"><EMAIL></p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <MapPin className="w-5 h-5 text-forest-green-600 mt-1" />
                  <div>
                    <h3 className="font-semibold text-forest-grey-800">Address</h3>
                    <p className="text-forest-grey-600">
                      Industrial Area Road<br />
                      Nairobi, Kenya<br />
                      P.O. Box 12345-00100
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <Clock className="w-5 h-5 text-forest-green-600 mt-1" />
                  <div>
                    <h3 className="font-semibold text-forest-grey-800">Business Hours</h3>
                    <p className="text-forest-grey-600">
                      Monday - Friday: 8:00 AM - 6:00 PM<br />
                      Saturday: 8:00 AM - 4:00 PM<br />
                      Sunday: Closed
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="text-forest-grey-800">Bulk Pricing</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-forest-grey-600 mb-4">
                  Looking for bulk quantities? We offer competitive pricing for large orders and construction projects.
                </p>
                <ul className="text-sm text-forest-grey-600 space-y-1">
                  <li>• Volume discounts available</li>
                  <li>• Custom delivery schedules</li>
                  <li>• Dedicated account manager</li>
                  <li>• Extended payment terms</li>
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* Contact Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-forest-grey-800">Send us a Message</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">Full Name *</Label>
                      <Input
                        id="name"
                        name="name"
                        type="text"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor="email">Email Address *</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="mt-1"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input
                        id="phone"
                        name="phone"
                        type="tel"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className="mt-1"
                        placeholder="+254 700 000 000"
                      />
                    </div>
                    <div>
                      <Label htmlFor="subject">Subject *</Label>
                      <Input
                        id="subject"
                        name="subject"
                        type="text"
                        value={formData.subject}
                        onChange={handleInputChange}
                        required
                        className="mt-1"
                        placeholder="e.g., Bulk pricing inquiry"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="message">Message *</Label>
                    <Textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      className="mt-1"
                      rows={6}
                      placeholder="Please provide details about your inquiry..."
                    />
                  </div>

                  <Button 
                    type="submit" 
                    className="w-full bg-forest-green-600 hover:bg-forest-green-700 text-white"
                  >
                    <Send className="w-4 h-4 mr-2" />
                    Send Message
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Delivery Team Section */}
        <div className="mt-12">
          <Card className="bg-gradient-to-r from-forest-green-600 to-forest-brown-600 text-white">
            <CardContent className="p-8">
              <div className="text-center">
                <h2 className="text-3xl font-bold mb-4">Delivery Team</h2>
                <p className="text-xl mb-6 text-forest-green-100">
                  Our professional delivery team ensures your materials arrive safely and on time.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                  <div>
                    <h3 className="font-semibold mb-2">Same Day Delivery</h3>
                    <p className="text-forest-green-100">Available within Nairobi for orders placed before 2 PM</p>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Nationwide Coverage</h3>
                    <p className="text-forest-green-100">We deliver to all major towns across Kenya</p>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Professional Handling</h3>
                    <p className="text-forest-green-100">Trained team ensures safe handling of all materials</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Contact;
