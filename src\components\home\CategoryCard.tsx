
import React from 'react';
import { Link } from 'react-router-dom';
import { <PERSON>, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';

interface CategoryCardProps {
  category: {
    title: string;
    description: string;
    image: string;
    products: string;
    link: string;
  }
}

const CategoryCard = ({ category }: CategoryCardProps) => {
  return (
    <Card className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 cursor-pointer h-full flex flex-col">
      <div className="relative overflow-hidden rounded-t-lg">
        <img 
          src={category.image} 
          alt={category.title}
          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
        />
        <div className="absolute top-2 right-2 bg-forest-rust-500 text-white px-2 py-1 rounded text-sm">
          {category.products}
        </div>
      </div>
      <CardHeader className="flex-grow">
        <CardTitle className="text-forest-grey-800 group-hover:text-forest-rust-600 transition-colors">
          {category.title}
        </CardTitle>
        <CardDescription>{category.description}</CardDescription>
      </CardHeader>
      <div className="p-4 pt-0">
        <Link to={`/products?category=${category.link.split('/').pop()}`}>
          <Button className="hardware-btn w-full justify-between bg-forest-steel-500 hover:bg-forest-steel-600 text-white">
            <span>Browse Collection</span>
            <ArrowRight className="w-4 h-4" />
          </Button>
        </Link>
      </div>
    </Card>
  );
};

export default CategoryCard;
