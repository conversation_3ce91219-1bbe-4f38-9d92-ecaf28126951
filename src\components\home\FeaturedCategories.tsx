
import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import CategoryCard from './CategoryCard';
import { categories } from '@/data/products';

const FeaturedCategories = () => {
  // Use first 4 categories for featured display
  const featuredCategories = categories.slice(0, 4).map(category => ({
    title: category.name,
    description: category.description,
    image: category.image,
    products: `${category.productCount}+ Products`,
    link: `/categories/${category.slug}`
  }));

  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-forest-grey-800 mb-4">Shop by Category</h2>
          <p className="text-lg text-forest-grey-600">Find exactly what you need for your project</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {featuredCategories.map((category, index) => (
            <CategoryCard key={index} category={category} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturedCategories;
