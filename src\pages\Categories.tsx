
import { Package } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Header from '@/components/Header';
import { Link } from 'react-router-dom';
import { categories } from '@/data/products';

const Categories = () => {

  return (
    <div className="min-h-screen bg-forest-grey-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-forest-grey-800 mb-4">Product Categories</h1>
          <p className="text-lg text-forest-grey-600">Browse our extensive range of building materials organized by category</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {categories.map((category, index) => (
            <Link key={category.id} to={`/products?category=${category.id}`}>
              <Card className="group hover:shadow-lg transition-all duration-300 overflow-hidden cursor-pointer">
                <div className="md:flex">
                  <div className="md:w-1/2">
                    <img
                      src={category.image}
                      alt={category.name}
                      className="w-full h-48 md:h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                  <div className="md:w-1/2 p-6">
                    <CardHeader className="p-0 mb-4">
                      <div className="flex items-center justify-between mb-2">
                        <CardTitle className="text-xl text-forest-grey-800 group-hover:text-forest-green-600 transition-colors">
                          {category.name}
                        </CardTitle>
                        <div className="flex items-center text-forest-green-600">
                          <Package className="w-4 h-4 mr-1" />
                          <span className="text-sm font-medium">{category.productCount}</span>
                        </div>
                      </div>
                      <CardDescription className="text-forest-grey-600">
                        {category.description}
                      </CardDescription>
                    </CardHeader>

                    <CardContent className="p-0">
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-forest-grey-700 mb-2">Subcategories:</h4>
                        <div className="flex flex-wrap gap-1">
                          {category.subcategories.map((sub, subIndex) => (
                            <span
                              key={subIndex}
                              className="text-xs bg-forest-grey-100 text-forest-grey-600 px-2 py-1 rounded"
                            >
                              {sub}
                            </span>
                          ))}
                        </div>
                      </div>

                      <Button className="w-full btn-primary">
                        Browse {category.name}
                      </Button>
                    </CardContent>
                  </div>
                </div>
              </Card>
            </Link>
          ))}
        </div>

        {/* Call to Action */}
        <div className="mt-16 bg-gradient-to-r from-forest-green-600 to-forest-brown-600 rounded-lg p-8 text-white text-center">
          <h2 className="text-3xl font-bold mb-4">Can't Find What You're Looking For?</h2>
          <p className="text-xl mb-6 text-forest-green-100">
            Our expert team can help you find the right materials for your specific project needs.
          </p>
          <Button size="lg" variant="outline" className="text-white border-white hover:bg-white hover:text-forest-green-600" asChild>
            <Link to="/contact">Contact Our Experts</Link>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Categories;
