
import { useState } from 'react';
import { Search, ShoppingCart, Home, Package, Truck, Construction } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Link, useNavigate } from 'react-router-dom';
import { useCart } from '@/contexts/CartContext';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const { state } = useCart();
  const navigate = useNavigate();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      navigate(`/products?search=${encodeURIComponent(searchTerm.trim())}`);
      setSearchTerm('');
    }
  };
  
  return (
    <header className="bg-white border-b border-gray-100 sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between py-4">
          <div className="flex items-center space-x-2">
            <div className="w-10 h-10 bg-forest-rust-500 rounded-lg flex items-center justify-center">
              <Construction className="text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-forest-rust-600">DeepForest</h1>
              <p className="text-xs text-forest-grey-500">Building Materials</p>
            </div>
          </div>

          <div className="hidden md:flex items-center space-x-6">
            <Link to="/" className="text-forest-grey-700 hover:text-forest-rust-500 flex items-center">
              <Home className="w-4 h-4 mr-1" />
              <span>Home</span>
            </Link>
            <Link to="/products" className="text-forest-grey-700 hover:text-forest-rust-500 flex items-center">
              <Package className="w-4 h-4 mr-1" />
              <span>Products</span>
            </Link>
            <Link to="/categories" className="text-forest-grey-700 hover:text-forest-rust-500 flex items-center">
              <Package className="w-4 h-4 mr-1" />
              <span>Categories</span>
            </Link>
            <Link to="/sales" className="text-forest-grey-700 hover:text-forest-rust-500 flex items-center">
              <Package className="w-4 h-4 mr-1" />
              <span>Sales</span>
            </Link>
            <Link to="/delivery" className="text-forest-grey-700 hover:text-forest-rust-500 flex items-center">
              <Truck className="w-4 h-4 mr-1" />
              <span>Delivery</span>
            </Link>
          </div>

          <div className="flex items-center space-x-4">
            <form onSubmit={handleSearch} className="hidden lg:block relative w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-forest-grey-400 w-4 h-4" />
              <Input
                type="text"
                placeholder="Search products..."
                className="pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </form>
            
            <Link to="/cart">
              <Button variant="ghost" size="sm" className="relative">
                <ShoppingCart className="w-5 h-5" />
                {state.itemCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    {state.itemCount}
                  </span>
                )}
              </Button>
            </Link>
            
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                className="h-6 w-6"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </Button>
          </div>
        </div>

        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="md:hidden py-4">
            <div className="flex flex-col space-y-3">
              <Link to="/" className="text-forest-grey-700 hover:text-forest-rust-500 flex items-center">
                <Home className="w-4 h-4 mr-2" />
                <span>Home</span>
              </Link>
              <Link to="/products" className="text-forest-grey-700 hover:text-forest-rust-500 flex items-center">
                <Package className="w-4 h-4 mr-2" />
                <span>Products</span>
              </Link>
              <Link to="/categories" className="text-forest-grey-700 hover:text-forest-rust-500 flex items-center">
                <Package className="w-4 h-4 mr-2" />
                <span>Categories</span>
              </Link>
              <Link to="/sales" className="text-forest-grey-700 hover:text-forest-rust-500 flex items-center">
                <Package className="w-4 h-4 mr-2" />
                <span>Sales</span>
              </Link>
              <Link to="/delivery" className="text-forest-grey-700 hover:text-forest-rust-500 flex items-center">
                <Truck className="w-4 h-4 mr-2" />
                <span>Delivery</span>
              </Link>
              <Link to="/cart" className="text-forest-grey-700 hover:text-forest-rust-500 flex items-center">
                <ShoppingCart className="w-4 h-4 mr-2" />
                <span>Cart</span>
              </Link>
            </div>
            <div className="mt-4">
              <form onSubmit={handleSearch} className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-forest-grey-400 w-4 h-4" />
                <Input
                  type="text"
                  placeholder="Search products..."
                  className="pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </form>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
