export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  image: string;
  category: string;
  inStock: boolean;
  onSale: boolean;
  weight?: number; // in KG
  sku: string;
}

export interface Category {
  id: string;
  name: string;
  description: string;
  image: string;
  productCount: number;
  subcategories: string[];
  slug: string;
}

export const categories: Category[] = [
  {
    id: "doors",
    name: "Doors & Windows",
    description: "Interior and exterior doors, windows, frames, and hardware. Find the perfect entrance solutions for your project.",
    image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=500",
    productCount: 156,
    subcategories: ["Interior Doors", "Exterior Doors", "Windows", "Door Hardware", "Frames"],
    slug: "doors"
  },
  {
    id: "paint",
    name: "Paints & Coatings",
    description: "Professional grade paints, primers, and protective coatings for all surfaces and applications.",
    image: "https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=500",
    productCount: 243,
    subcategories: ["Interior Paint", "Exterior Paint", "Primers", "Wood Stains", "Protective Coatings"],
    slug: "paint"
  },
  {
    id: "cement",
    name: "Cement & Concrete",
    description: "High-quality cement, concrete mixes, and masonry supplies for strong, durable construction.",
    image: "https://images.unsplash.com/photo-1504307651254-35680f356dfd?w=500",
    productCount: 89,
    subcategories: ["Portland Cement", "Ready Mix", "Concrete Blocks", "Mortar", "Additives"],
    slug: "cement"
  },
  {
    id: "hardware",
    name: "Hardware & Tools",
    description: "Essential tools, fasteners, and hardware for construction and maintenance projects.",
    image: "https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=500",
    productCount: 312,
    subcategories: ["Hand Tools", "Power Tools", "Fasteners", "Hardware", "Safety Equipment"],
    slug: "hardware"
  },
  {
    id: "roofing",
    name: "Roofing Materials",
    description: "Complete roofing solutions including tiles, sheets, gutters, and accessories.",
    image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500",
    productCount: 127,
    subcategories: ["Roofing Tiles", "Metal Sheets", "Gutters", "Insulation", "Accessories"],
    slug: "roofing"
  },
  {
    id: "electrical",
    name: "Electrical Supplies",
    description: "Wiring, fixtures, switches, and electrical components for residential and commercial use.",
    image: "https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=500",
    productCount: 198,
    subcategories: ["Wiring", "Switches & Outlets", "Lighting", "Circuit Breakers", "Conduits"],
    slug: "electrical"
  }
];

export const products: Product[] = [
  // Doors & Windows
  {
    id: "door-001",
    name: "Solid Wood Interior Door",
    description: "Premium mahogany interior door with elegant finish",
    price: 15000,
    originalPrice: 18000,
    image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400",
    category: "doors",
    inStock: true,
    onSale: true,
    weight: 25,
    sku: "DW-001"
  },
  {
    id: "door-002",
    name: "Steel Security Door",
    description: "Heavy-duty steel security door with multiple locks",
    price: 25000,
    image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400",
    category: "doors",
    inStock: true,
    onSale: false,
    weight: 45,
    sku: "DW-002"
  },
  {
    id: "window-001",
    name: "Aluminum Sliding Window",
    description: "Double-glazed aluminum sliding window",
    price: 12000,
    image: "https://images.unsplash.com/photo-1545558014-8692077e9b5c?w=400",
    category: "doors",
    inStock: true,
    onSale: false,
    weight: 18,
    sku: "DW-003"
  },
  
  // Paints & Coatings
  {
    id: "paint-001",
    name: "Premium Interior Paint - White",
    description: "High-quality latex paint for interior walls",
    price: 3500,
    originalPrice: 4000,
    image: "https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=400",
    category: "paint",
    inStock: true,
    onSale: true,
    weight: 4,
    sku: "PT-001"
  },
  {
    id: "paint-002",
    name: "Exterior Weather Shield Paint",
    description: "Weather-resistant exterior paint with UV protection",
    price: 4200,
    image: "https://images.unsplash.com/photo-1589939705384-5185137a7f0f?w=400",
    category: "paint",
    inStock: true,
    onSale: false,
    weight: 4.5,
    sku: "PT-002"
  },
  {
    id: "paint-003",
    name: "Wood Stain - Mahogany",
    description: "Premium wood stain for furniture and decking",
    price: 2800,
    image: "https://images.unsplash.com/photo-1604709177225-055f99402ea3?w=400",
    category: "paint",
    inStock: false,
    onSale: false,
    weight: 2.5,
    sku: "PT-003"
  },

  // Cement & Concrete
  {
    id: "cement-001",
    name: "Portland Cement - 50kg",
    description: "High-grade Portland cement for construction",
    price: 800,
    image: "https://images.unsplash.com/photo-1504307651254-35680f356dfd?w=400",
    category: "cement",
    inStock: true,
    onSale: false,
    weight: 50,
    sku: "CM-001"
  },
  {
    id: "cement-002",
    name: "Ready Mix Concrete",
    description: "Pre-mixed concrete for quick projects",
    price: 1200,
    originalPrice: 1400,
    image: "https://images.unsplash.com/photo-1581094794329-c8112a89af12?w=400",
    category: "cement",
    inStock: true,
    onSale: true,
    weight: 40,
    sku: "CM-002"
  },

  // Hardware & Tools
  {
    id: "tool-001",
    name: "Professional Hammer Set",
    description: "Set of 3 professional hammers for construction",
    price: 2500,
    image: "https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400",
    category: "hardware",
    inStock: true,
    onSale: false,
    weight: 2.2,
    sku: "HW-001"
  },
  {
    id: "tool-002",
    name: "Electric Drill Kit",
    description: "Cordless electric drill with bits and case",
    price: 8500,
    originalPrice: 10000,
    image: "https://images.unsplash.com/photo-1572981779307-38b8cabb2407?w=400",
    category: "hardware",
    inStock: true,
    onSale: true,
    weight: 1.8,
    sku: "HW-002"
  },
  {
    id: "fastener-001",
    name: "Stainless Steel Screws - Assorted",
    description: "Assorted pack of stainless steel screws",
    price: 1200,
    image: "https://images.unsplash.com/photo-1609205807107-e8ec2120f9de?w=400",
    category: "hardware",
    inStock: true,
    onSale: false,
    weight: 0.5,
    sku: "HW-003"
  },

  // Roofing Materials
  {
    id: "roof-001",
    name: "Clay Roofing Tiles",
    description: "Traditional clay tiles for durable roofing",
    price: 150,
    image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400",
    category: "roofing",
    inStock: true,
    onSale: false,
    weight: 2.5,
    sku: "RF-001"
  },
  {
    id: "roof-002",
    name: "Galvanized Iron Sheets",
    description: "Corrugated galvanized iron roofing sheets",
    price: 1800,
    originalPrice: 2000,
    image: "https://images.unsplash.com/photo-1590736969955-71cc94901144?w=400",
    category: "roofing",
    inStock: true,
    onSale: true,
    weight: 8,
    sku: "RF-002"
  },

  // Electrical Supplies
  {
    id: "elec-001",
    name: "Copper Electrical Wire - 100m",
    description: "High-quality copper wire for electrical installations",
    price: 3200,
    image: "https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=400",
    category: "electrical",
    inStock: true,
    onSale: false,
    weight: 12,
    sku: "EL-001"
  },
  {
    id: "elec-002",
    name: "LED Light Fixtures Set",
    description: "Energy-efficient LED light fixtures for home",
    price: 4500,
    originalPrice: 5200,
    image: "https://images.unsplash.com/photo-1524484485831-a92ffc0de03f?w=400",
    category: "electrical",
    inStock: true,
    onSale: true,
    weight: 3.2,
    sku: "EL-002"
  }
];

// Featured products for homepage
export const featuredProducts = products.filter(product => product.onSale).slice(0, 8);

// Sale products for sales page
export const saleProducts = products.filter(product => product.onSale);
