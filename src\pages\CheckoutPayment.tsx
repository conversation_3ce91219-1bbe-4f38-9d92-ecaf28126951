import React, { useState } from 'react';
import { CreditCard, Smartphone, ArrowLeft, Lock } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Separator } from '@/components/ui/separator';
import { Link } from 'react-router-dom';
import Header from '@/components/Header';
import CurrencyFormatter from '@/components/ui/currency-formatter';
import { useCart } from '@/contexts/CartContext';

const CheckoutPayment = () => {
  const { state } = useCart();
  const [paymentMethod, setPaymentMethod] = useState('card');
  const [cardData, setCardData] = useState({
    number: '',
    expiry: '',
    cvc: '',
    name: ''
  });
  const [mpesaPhone, setMpesaPhone] = useState('');

  const handleCardInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCardData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // For now, just show an alert - this will be connected to payment gateway later
    alert('Payment processing is not yet implemented. This is a placeholder for future payment integration.');
  };

  const deliveryFee = 500; // KSH 500 delivery fee
  const totalWithDelivery = state.total + deliveryFee;

  return (
    <div className="min-h-screen bg-forest-grey-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Link to="/cart" className="inline-flex items-center text-forest-green-600 hover:text-forest-green-700">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Cart
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Payment Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center text-forest-grey-800">
                  <Lock className="w-5 h-5 mr-2" />
                  Secure Payment
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Payment Method Selection */}
                  <div>
                    <Label className="text-base font-semibold">Payment Method</Label>
                    <RadioGroup value={paymentMethod} onValueChange={setPaymentMethod} className="mt-3">
                      <div className="flex items-center space-x-2 p-3 border rounded-lg">
                        <RadioGroupItem value="card" id="card" />
                        <CreditCard className="w-5 h-5 text-forest-grey-600" />
                        <Label htmlFor="card" className="flex-1 cursor-pointer">Credit/Debit Card</Label>
                      </div>
                      <div className="flex items-center space-x-2 p-3 border rounded-lg">
                        <RadioGroupItem value="mpesa" id="mpesa" />
                        <Smartphone className="w-5 h-5 text-forest-grey-600" />
                        <Label htmlFor="mpesa" className="flex-1 cursor-pointer">M-Pesa (Coming Soon)</Label>
                      </div>
                    </RadioGroup>
                  </div>

                  {/* Card Payment Form */}
                  {paymentMethod === 'card' && (
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="cardName">Cardholder Name</Label>
                        <Input
                          id="cardName"
                          name="name"
                          type="text"
                          value={cardData.name}
                          onChange={handleCardInputChange}
                          placeholder="John Doe"
                          className="mt-1"
                          required
                        />
                      </div>

                      <div>
                        <Label htmlFor="cardNumber">Card Number</Label>
                        <Input
                          id="cardNumber"
                          name="number"
                          type="text"
                          value={cardData.number}
                          onChange={handleCardInputChange}
                          placeholder="1234 5678 9012 3456"
                          className="mt-1"
                          maxLength={19}
                          required
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="expiry">Expiry Date</Label>
                          <Input
                            id="expiry"
                            name="expiry"
                            type="text"
                            value={cardData.expiry}
                            onChange={handleCardInputChange}
                            placeholder="MM/YY"
                            className="mt-1"
                            maxLength={5}
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="cvc">CVC</Label>
                          <Input
                            id="cvc"
                            name="cvc"
                            type="text"
                            value={cardData.cvc}
                            onChange={handleCardInputChange}
                            placeholder="123"
                            className="mt-1"
                            maxLength={4}
                            required
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  {/* M-Pesa Payment Form */}
                  {paymentMethod === 'mpesa' && (
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="mpesaPhone">M-Pesa Phone Number</Label>
                        <Input
                          id="mpesaPhone"
                          type="tel"
                          value={mpesaPhone}
                          onChange={(e) => setMpesaPhone(e.target.value)}
                          placeholder="+254 700 000 000"
                          className="mt-1"
                          required
                        />
                      </div>
                      <div className="bg-forest-green-50 p-4 rounded-lg">
                        <p className="text-sm text-forest-green-700">
                          <strong>Note:</strong> M-Pesa integration is coming soon. For now, please use card payment or contact us for alternative payment methods.
                        </p>
                      </div>
                    </div>
                  )}

                  <Separator />

                  {/* Security Notice */}
                  <div className="bg-forest-grey-50 p-4 rounded-lg">
                    <div className="flex items-center space-x-2 text-sm text-forest-grey-600">
                      <Lock className="w-4 h-4" />
                      <span>Your payment information is encrypted and secure</span>
                    </div>
                  </div>

                  <Button 
                    type="submit" 
                    className="w-full bg-forest-green-600 hover:bg-forest-green-700 text-white"
                    disabled={paymentMethod === 'mpesa'}
                  >
                    {paymentMethod === 'mpesa' ? 'M-Pesa Coming Soon' : `Pay ${totalWithDelivery.toLocaleString()} KSH`}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="text-forest-grey-800">Order Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {state.items.map((item) => (
                  <div key={item.id} className="flex justify-between items-center">
                    <div className="flex-1">
                      <p className="font-medium text-forest-grey-800">{item.name}</p>
                      <p className="text-sm text-forest-grey-600">Qty: {item.quantity}</p>
                    </div>
                    <p className="font-medium text-forest-green-600">
                      <CurrencyFormatter amount={item.price * item.quantity} />
                    </p>
                  </div>
                ))}

                <Separator />

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-forest-grey-600">Subtotal</span>
                    <span className="font-medium">
                      <CurrencyFormatter amount={state.total} />
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-forest-grey-600">Delivery</span>
                    <span className="font-medium">
                      <CurrencyFormatter amount={deliveryFee} />
                    </span>
                  </div>
                  <Separator />
                  <div className="flex justify-between text-lg font-bold">
                    <span className="text-forest-grey-800">Total</span>
                    <span className="text-forest-green-600">
                      <CurrencyFormatter amount={totalWithDelivery} />
                    </span>
                  </div>
                </div>

                <div className="bg-forest-green-50 p-3 rounded-lg">
                  <p className="text-sm text-forest-green-700">
                    <strong>Free delivery</strong> on orders over KSH 10,000 within Nairobi
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPayment;
